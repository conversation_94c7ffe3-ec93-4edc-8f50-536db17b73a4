#!/usr/bin/env python3
"""
策略回测命令行工具
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import argparse
from datetime import datetime, timed<PERSON>ta
from typing import List

from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.backtest.backtest_manager import BacktestManager
from src.core.interfaces.backtest import BacktestMode


def parse_date(date_str: str) -> datetime:
    """解析日期字符串"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")


def parse_stock_list(stock_str: str) -> List[str]:
    """解析股票列表"""
    if not stock_str:
        return None
    return [code.strip() for code in stock_str.split(',') if code.strip()]


def main():
    parser = argparse.ArgumentParser(description='策略回测工具')
    
    # 基本参数
    parser.add_argument('strategy', help='策略名称 (如: technical_reversal)')
    parser.add_argument('--start-date', type=parse_date, required=True,
                       help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=parse_date, required=True,
                       help='回测结束日期 (YYYY-MM-DD)')
    
    # 回测模式
    parser.add_argument('--mode', choices=['single', 'rolling', 'batch'],
                       default='rolling', help='回测模式 (默认: rolling)')
    
    # 回测参数
    parser.add_argument('--frequency', type=int, default=7,
                       help='回测频率（天）(默认: 7)')
    parser.add_argument('--holding-days', type=int, default=10,
                       help='持有天数 (默认: 10)')
    parser.add_argument('--initial-capital', type=float, default=100000.0,
                       help='初始资金 (默认: 100000)')
    parser.add_argument('--max-positions', type=int, default=10,
                       help='最大持仓数 (默认: 10)')
    parser.add_argument('--commission-rate', type=float, default=0.0003,
                       help='手续费率 (默认: 0.0003)')
    parser.add_argument('--slippage-rate', type=float, default=0.001,
                       help='滑点率 (默认: 0.001)')
    
    # 股票池
    parser.add_argument('--stock-pool', type=parse_stock_list,
                       help='股票池，用逗号分隔 (如: 000001,000002,600000)')
    
    # 输出选项
    parser.add_argument('--output', help='输出报告到文件')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    # 批量回测
    parser.add_argument('--batch-strategies', type=str,
                       help='批量回测多个策略，用逗号分隔')
    
    # 滚动窗口回测
    parser.add_argument('--rolling-window', type=int,
                       help='滚动窗口回测，指定窗口月数')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.start_date >= args.end_date:
        print("错误: 开始日期必须早于结束日期")
        return 1
    
    try:
        # 初始化数据访问
        config = db_config.get_mysql_config()
        data_access = MySQLDataAccess(**config)
        
        # 创建回测管理器
        backtest_manager = BacktestManager(data_access)
        
        print("=" * 80)
        print("策略回测工具")
        print("=" * 80)
        
        if args.batch_strategies:
            # 批量回测
            strategies = [s.strip() for s in args.batch_strategies.split(',')]
            print(f"开始批量回测策略: {', '.join(strategies)}")
            
            results = backtest_manager.run_batch_backtest(
                strategies=strategies,
                start_date=args.start_date,
                end_date=args.end_date,
                mode=BacktestMode(args.mode),
                frequency=args.frequency,
                holding_days=args.holding_days,
                initial_capital=args.initial_capital,
                max_positions=args.max_positions,
                commission_rate=args.commission_rate,
                slippage_rate=args.slippage_rate,
                stock_pool=args.stock_pool
            )
            
            # 生成比较报告
            report = backtest_manager.compare_strategies(results)
            print(report)
            
            # 为每个策略生成详细报告
            if args.verbose:
                for strategy_name, result in results.items():
                    print(f"\n{strategy_name} 详细报告:")
                    print("-" * 80)
                    detailed_report = backtest_manager.generate_backtest_report(result)
                    print(detailed_report)
        
        elif args.rolling_window:
            # 滚动窗口回测
            print(f"开始滚动窗口回测策略: {args.strategy}")
            print(f"窗口大小: {args.rolling_window} 个月")
            
            results = backtest_manager.run_rolling_window_backtest(
                strategy_name=args.strategy,
                start_date=args.start_date,
                end_date=args.end_date,
                window_months=args.rolling_window,
                mode=BacktestMode(args.mode),
                frequency=args.frequency,
                holding_days=args.holding_days,
                initial_capital=args.initial_capital,
                max_positions=args.max_positions,
                commission_rate=args.commission_rate,
                slippage_rate=args.slippage_rate,
                stock_pool=args.stock_pool
            )
            
            # 汇总滚动窗口结果
            print(f"\n滚动窗口回测结果汇总:")
            print("-" * 80)
            print(f"{'窗口期间':<25} {'交易次数':<8} {'胜率':<8} {'平均收益':<10} {'最大回撤':<10}")
            print("-" * 80)
            
            for result in results:
                period = f"{result.config.start_date.date()} - {result.config.end_date.date()}"
                print(f"{period:<25} {result.total_trades:<8} {result.win_rate:<7.1f}% "
                      f"{result.avg_return_per_trade:<9.2f}% {result.max_drawdown:<9.2f}%")
            
            # 整体统计
            if results:
                total_trades = sum(r.total_trades for r in results)
                avg_win_rate = sum(r.win_rate for r in results) / len(results)
                avg_return = sum(r.avg_return_per_trade for r in results) / len(results)
                max_drawdown = max(r.max_drawdown for r in results)
                
                print("-" * 80)
                print(f"{'整体统计':<25} {total_trades:<8} {avg_win_rate:<7.1f}% "
                      f"{avg_return:<9.2f}% {max_drawdown:<9.2f}%")
        
        else:
            # 单策略回测
            print(f"开始回测策略: {args.strategy}")
            print(f"回测期间: {args.start_date.date()} 到 {args.end_date.date()}")
            
            result = backtest_manager.run_strategy_backtest(
                strategy_name=args.strategy,
                start_date=args.start_date,
                end_date=args.end_date,
                mode=BacktestMode(args.mode),
                frequency=args.frequency,
                holding_days=args.holding_days,
                initial_capital=args.initial_capital,
                max_positions=args.max_positions,
                commission_rate=args.commission_rate,
                slippage_rate=args.slippage_rate,
                stock_pool=args.stock_pool
            )
            
            # 生成报告
            report = backtest_manager.generate_backtest_report(result)
            print(report)
        
        # 保存报告到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                if args.batch_strategies:
                    f.write(backtest_manager.compare_strategies(results))
                    if args.verbose:
                        for strategy_name, result in results.items():
                            f.write(f"\n\n{strategy_name} 详细报告:\n")
                            f.write(backtest_manager.generate_backtest_report(result))
                else:
                    f.write(report)
            print(f"\n报告已保存到: {args.output}")
        
        return 0
        
    except Exception as e:
        print(f"回测失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
    
    finally:
        try:
            data_access.close_connection()
        except:
            pass


if __name__ == "__main__":
    exit(main())
