#!/usr/bin/env python3
"""
回测功能示例
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime, timedelta
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.backtest.backtest_manager import BacktestManager
from src.core.interfaces.backtest import BacktestMode


def example_single_strategy_backtest():
    """单策略回测示例"""
    print("=" * 80)
    print("单策略回测示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建回测管理器
        backtest_manager = BacktestManager(data_access)
        
        # 设置回测参数
        strategy_name = "technical_reversal"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 6, 30)
        
        print(f"策略: {strategy_name}")
        print(f"回测期间: {start_date.date()} 到 {end_date.date()}")
        
        # 执行回测
        result = backtest_manager.run_strategy_backtest(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            mode=BacktestMode.ROLLING,
            frequency=7,  # 每周回测一次
            holding_days=10,  # 持有10天
            initial_capital=100000.0,
            max_positions=5,  # 最多持有5只股票
            commission_rate=0.0003,
            slippage_rate=0.001
        )
        
        # 生成报告
        report = backtest_manager.generate_backtest_report(result)
        print(report)
        
        return result
        
    except Exception as e:
        print(f"回测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_batch_backtest():
    """批量回测示例"""
    print("\n" + "=" * 80)
    print("批量回测示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建回测管理器
        backtest_manager = BacktestManager(data_access)
        
        # 设置回测参数
        strategies = ["technical_reversal", "volume_anomaly"]
        start_date = datetime(2024, 3, 1)
        end_date = datetime(2024, 5, 31)
        
        print(f"策略: {', '.join(strategies)}")
        print(f"回测期间: {start_date.date()} 到 {end_date.date()}")
        
        # 执行批量回测
        results = backtest_manager.run_batch_backtest(
            strategies=strategies,
            start_date=start_date,
            end_date=end_date,
            mode=BacktestMode.ROLLING,
            frequency=14,  # 每两周回测一次
            holding_days=7,  # 持有7天
            initial_capital=100000.0,
            max_positions=8,
            commission_rate=0.0003,
            slippage_rate=0.001
        )
        
        # 生成比较报告
        if results:
            comparison_report = backtest_manager.compare_strategies(results)
            print(comparison_report)
            
            # 显示每个策略的详细信息
            print("\n详细回测结果:")
            print("=" * 80)
            for strategy_name, result in results.items():
                print(f"\n{strategy_name} 策略详细报告:")
                print("-" * 60)
                detailed_report = backtest_manager.generate_backtest_report(result)
                print(detailed_report)
        
        return results
        
    except Exception as e:
        print(f"批量回测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def example_rolling_window_backtest():
    """滚动窗口回测示例"""
    print("\n" + "=" * 80)
    print("滚动窗口回测示例")
    print("=" * 80)
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 创建回测管理器
        backtest_manager = BacktestManager(data_access)
        
        # 设置回测参数
        strategy_name = "technical_reversal"
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        window_months = 3  # 3个月窗口
        
        print(f"策略: {strategy_name}")
        print(f"回测期间: {start_date.date()} 到 {end_date.date()}")
        print(f"滚动窗口: {window_months} 个月")
        
        # 执行滚动窗口回测
        results = backtest_manager.run_rolling_window_backtest(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            window_months=window_months,
            mode=BacktestMode.ROLLING,
            frequency=7,
            holding_days=10,
            initial_capital=100000.0,
            max_positions=5,
            commission_rate=0.0003,
            slippage_rate=0.001
        )
        
        # 分析滚动窗口结果
        if results:
            print(f"\n滚动窗口回测结果:")
            print("-" * 80)
            print(f"{'窗口期间':<30} {'交易次数':<8} {'胜率':<8} {'平均收益':<10} {'最大回撤':<10}")
            print("-" * 80)
            
            total_trades = 0
            total_win_rate = 0
            total_avg_return = 0
            max_drawdown = 0
            
            for i, result in enumerate(results):
                period = f"{result.config.start_date.date()} - {result.config.end_date.date()}"
                print(f"{period:<30} {result.total_trades:<8} {result.win_rate:<7.1f}% "
                      f"{result.avg_return_per_trade:<9.2f}% {result.max_drawdown:<9.2f}%")
                
                total_trades += result.total_trades
                total_win_rate += result.win_rate
                total_avg_return += result.avg_return_per_trade
                max_drawdown = max(max_drawdown, result.max_drawdown)
            
            # 计算平均值
            if results:
                avg_win_rate = total_win_rate / len(results)
                avg_return = total_avg_return / len(results)
                
                print("-" * 80)
                print(f"{'平均表现':<30} {total_trades:<8} {avg_win_rate:<7.1f}% "
                      f"{avg_return:<9.2f}% {max_drawdown:<9.2f}%")
                
                # 策略稳定性分析
                win_rates = [r.win_rate for r in results]
                returns = [r.avg_return_per_trade for r in results]
                
                import numpy as np
                win_rate_std = np.std(win_rates)
                return_std = np.std(returns)
                
                print(f"\n策略稳定性分析:")
                print(f"胜率标准差: {win_rate_std:.2f}%")
                print(f"收益率标准差: {return_std:.2f}%")
                
                if win_rate_std < 10 and return_std < 2:
                    print("✓ 策略表现稳定")
                elif win_rate_std < 20 and return_std < 5:
                    print("△ 策略表现较为稳定")
                else:
                    print("✗ 策略表现不稳定，波动较大")
        
        return results
        
    except Exception as e:
        print(f"滚动窗口回测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        data_access.close_connection()


def main():
    """主函数"""
    print("策略回测功能示例")
    print("=" * 80)
    
    # 运行不同类型的回测示例
    
    # 1. 单策略回测
    single_result = example_single_strategy_backtest()
    
    # 2. 批量回测
    batch_results = example_batch_backtest()
    
    # 3. 滚动窗口回测
    rolling_results = example_rolling_window_backtest()
    
    print("\n" + "=" * 80)
    print("所有回测示例完成")
    print("=" * 80)
    
    # 总结
    print("\n回测功能总结:")
    print("1. 单策略回测 - 测试单个策略在指定时间段的表现")
    print("2. 批量回测 - 同时测试多个策略并进行比较")
    print("3. 滚动窗口回测 - 测试策略在不同时间窗口的稳定性")
    print("\n使用命令行工具进行更灵活的回测:")
    print("python scripts/run_backtest.py technical_reversal --start-date 2024-01-01 --end-date 2024-06-30")


if __name__ == "__main__":
    main()
