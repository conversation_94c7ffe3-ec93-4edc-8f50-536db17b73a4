#!/usr/bin/env python3
"""
技术反转策略回测
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config
from src.strategies.technical_reversal_strategy import TechnicalReversalStrategy

def backtest_strategy():
    """回测技术反转策略"""
    print("开始技术反转策略回测...")
    
    # 初始化数据访问
    config = db_config.get_mysql_config()
    data_access = MySQLDataAccess(**config)
    
    try:
        # 定义回测时间段
        backtest_periods = [
            # 2024年不同月份
            (datetime(2024, 3, 1), datetime(2024, 3, 31), "2024年3月"),
            (datetime(2024, 4, 1), datetime(2024, 4, 30), "2024年4月"),
            (datetime(2024, 5, 1), datetime(2024, 5, 31), "2024年5月"),
            (datetime(2024, 6, 1), datetime(2024, 6, 30), "2024年6月"),
            (datetime(2024, 9, 1), datetime(2024, 9, 30), "2024年9月"),
            (datetime(2024, 10, 1), datetime(2024, 10, 31), "2024年10月"),
            # 2025年
            (datetime(2025, 1, 1), datetime(2025, 1, 31), "2025年1月"),
            (datetime(2025, 2, 1), datetime(2025, 2, 28), "2025年2月"),
            (datetime(2025, 3, 1), datetime(2025, 3, 31), "2025年3月"),
            (datetime(2025, 4, 1), datetime(2025, 4, 30), "2025年4月"),
        ]
        
        all_results = []
        
        for start_date, end_date, period_name in backtest_periods:
            print(f"\n回测期间: {period_name}")
            print("-" * 50)
            
            # 在该时间段内每周执行一次策略
            current_date = start_date
            period_results = []
            
            while current_date <= end_date:
                # 跳过周末
                if current_date.weekday() < 5:  # 周一到周五
                    results = run_strategy_on_date(data_access, current_date)
                    if results:
                        period_results.extend(results)
                        print(f"  {current_date.date()}: 选中 {len(results)} 只股票")
                
                current_date += timedelta(days=7)  # 每周测试一次
            
            if period_results:
                # 分析该期间的表现
                performance = analyze_period_performance(data_access, period_results, period_name)
                all_results.append({
                    'period': period_name,
                    'selections': period_results,
                    'performance': performance
                })
                
                print(f"  期间总选股: {len(period_results)} 次")
                print(f"  平均收益: {performance['avg_return']:.2f}%")
                print(f"  胜率: {performance['win_rate']:.1f}%")
            else:
                print(f"  该期间未选中任何股票")
        
        # 生成回测报告
        generate_backtest_report(all_results)
        
        return all_results
        
    except Exception as e:
        print(f"回测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        data_access.close_connection()

def run_strategy_on_date(data_access, target_date):
    """在指定日期运行策略"""
    try:
        # 创建策略实例
        strategy = TechnicalReversalStrategy()
        
        # 获取所有股票代码（限制数量以加快测试）
        all_stock_codes = data_access.get_all_stock_codes()
        # 随机选择一部分股票进行测试，加快速度
        import random
        test_stock_codes = random.sample(all_stock_codes, min(100, len(all_stock_codes)))
        
        results = []
        
        # 计算需要的历史数据范围
        start_date = target_date - timedelta(days=strategy.config['baseline_days'] + 10)
        
        for stock_code in test_stock_codes:
            try:
                # 获取股票基本信息
                stock_info = data_access.get_stock_info(stock_code)
                if not stock_info:
                    continue
                
                # 过滤ST股票
                if strategy.config['exclude_st'] and strategy._is_st_stock(stock_info['stock_name']):
                    continue
                
                # 获取到目标日期为止的交易数据
                trading_data = data_access.get_stock_data(stock_code, start_date, target_date)
                if len(trading_data) < strategy.config['baseline_days']:
                    continue
                
                # 分析技术反转信号
                reversal_result = strategy._analyze_technical_reversal(stock_code, stock_info, trading_data)
                if reversal_result:
                    reversal_result['selection_date'] = target_date.date()
                    results.append(reversal_result)
            
            except Exception as e:
                continue
        
        # 按评分排序并限制结果数量
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:strategy.config['max_results']]
        
    except Exception as e:
        print(f"在日期 {target_date.date()} 运行策略失败: {str(e)}")
        return []

def analyze_period_performance(data_access, selections, period_name):
    """分析期间表现"""
    try:
        returns = []
        win_count = 0
        total_count = 0
        
        for selection in selections:
            stock_code = selection['stock_code']
            selection_date = selection['selection_date']
            selection_price = selection['close_price']
            
            # 计算后续10个交易日的收益
            future_start = datetime.combine(selection_date, datetime.min.time()) + timedelta(days=1)
            future_end = future_start + timedelta(days=15)  # 多取几天确保有10个交易日
            
            future_data = data_access.get_stock_data(stock_code, future_start, future_end)
            
            if len(future_data) >= 5:  # 至少要有5个交易日的数据
                # 取前10个交易日（如果有的话）
                future_data = future_data[:10]
                
                # 计算最大收益和最终收益
                max_price = max(float(data['high_price']) for data in future_data)
                final_price = float(future_data[-1]['close_price'])
                
                max_return = (max_price / selection_price - 1) * 100
                final_return = (final_price / selection_price - 1) * 100
                
                returns.append({
                    'stock_code': stock_code,
                    'max_return': max_return,
                    'final_return': final_return,
                    'selection_date': selection_date
                })
                
                total_count += 1
                if max_return > 0:  # 如果最大收益为正，算作成功
                    win_count += 1
        
        if total_count > 0:
            avg_max_return = sum(r['max_return'] for r in returns) / len(returns)
            avg_final_return = sum(r['final_return'] for r in returns) / len(returns)
            win_rate = (win_count / total_count) * 100
            
            return {
                'total_selections': total_count,
                'avg_return': avg_final_return,
                'avg_max_return': avg_max_return,
                'win_rate': win_rate,
                'returns': returns
            }
        else:
            return {
                'total_selections': 0,
                'avg_return': 0,
                'avg_max_return': 0,
                'win_rate': 0,
                'returns': []
            }
    
    except Exception as e:
        print(f"分析期间表现失败: {str(e)}")
        return {
            'total_selections': 0,
            'avg_return': 0,
            'avg_max_return': 0,
            'win_rate': 0,
            'returns': []
        }

def generate_backtest_report(all_results):
    """生成回测报告"""
    print("\n" + "="*80)
    print("技术反转策略回测报告")
    print("="*80)
    
    if not all_results:
        print("没有回测数据")
        return
    
    # 总体统计
    total_selections = sum(r['performance']['total_selections'] for r in all_results)
    total_periods = len([r for r in all_results if r['performance']['total_selections'] > 0])
    
    print(f"\n总体统计:")
    print(f"回测期间数: {len(all_results)}")
    print(f"有效期间数: {total_periods}")
    print(f"总选股次数: {total_selections}")
    
    if total_selections > 0:
        # 计算加权平均收益
        weighted_avg_return = 0
        weighted_avg_max_return = 0
        total_wins = 0
        
        for result in all_results:
            perf = result['performance']
            if perf['total_selections'] > 0:
                weight = perf['total_selections'] / total_selections
                weighted_avg_return += perf['avg_return'] * weight
                weighted_avg_max_return += perf['avg_max_return'] * weight
                total_wins += perf['total_selections'] * (perf['win_rate'] / 100)
        
        overall_win_rate = (total_wins / total_selections) * 100
        
        print(f"整体平均收益: {weighted_avg_return:.2f}%")
        print(f"整体平均最大收益: {weighted_avg_max_return:.2f}%")
        print(f"整体胜率: {overall_win_rate:.1f}%")
    
    # 各期间详细表现
    print(f"\n各期间详细表现:")
    print("-" * 80)
    
    for result in all_results:
        period = result['period']
        perf = result['performance']
        
        print(f"{period}:")
        print(f"  选股次数: {perf['total_selections']}")
        if perf['total_selections'] > 0:
            print(f"  平均收益: {perf['avg_return']:.2f}%")
            print(f"  平均最大收益: {perf['avg_max_return']:.2f}%")
            print(f"  胜率: {perf['win_rate']:.1f}%")
            
            # 显示最佳案例
            if perf['returns']:
                best_case = max(perf['returns'], key=lambda x: x['max_return'])
                print(f"  最佳案例: {best_case['stock_code']} (最大收益: {best_case['max_return']:.2f}%)")
        else:
            print(f"  无选股")
        print()
    
    # 策略有效性评估
    print("策略有效性评估:")
    print("-" * 50)
    
    if total_selections > 0:
        if overall_win_rate > 60:
            print("✓ 策略表现优秀，胜率超过60%")
        elif overall_win_rate > 50:
            print("✓ 策略表现良好，胜率超过50%")
        elif overall_win_rate > 40:
            print("△ 策略表现一般，胜率在40-50%之间")
        else:
            print("✗ 策略表现较差，胜率低于40%")
        
        if weighted_avg_return > 3:
            print("✓ 平均收益表现优秀，超过3%")
        elif weighted_avg_return > 1:
            print("✓ 平均收益表现良好，超过1%")
        elif weighted_avg_return > 0:
            print("△ 平均收益为正，但较低")
        else:
            print("✗ 平均收益为负")
    else:
        print("✗ 策略在回测期间未选中任何股票，可能条件过于严格")

if __name__ == "__main__":
    print("="*80)
    print("技术反转策略回测")
    print("="*80)
    
    backtest_strategy()
