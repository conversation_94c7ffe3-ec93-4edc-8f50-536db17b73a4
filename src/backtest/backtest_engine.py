"""
回测引擎实现
"""
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from dataclasses import asdict

from ..core.interfaces.backtest import IBacktestEngine, BacktestConfig, BacktestResult, BacktestMode
from ..core.interfaces.data_access import IDataAccess
from ..core.interfaces.strategy import ISelectionStrategy
from ..strategies.strategy_manager import StrategyManager


class BacktestEngine(IBacktestEngine):
    """回测引擎实现"""

    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.strategy_manager = StrategyManager(data_access)
        self.logger = logging.getLogger(__name__)

    def run_backtest(self, config: BacktestConfig) -> BacktestResult:
        """运行回测"""
        self.logger.info(f"开始回测策略: {config.strategy_name}")
        self.logger.info(f"回测期间: {config.start_date.date()} 到 {config.end_date.date()}")

        start_time = datetime.now()

        try:
            # 创建策略实例
            strategy = self.strategy_manager.create_strategy(config.strategy_name)
            if not strategy:
                raise ValueError(f"无法创建策略: {config.strategy_name}")

            # 根据回测模式执行回测
            if config.mode == BacktestMode.ROLLING:
                trades = self._run_rolling_backtest(strategy, config)
            elif config.mode == BacktestMode.SINGLE:
                trades = self._run_single_backtest(strategy, config)
            elif config.mode == BacktestMode.BATCH:
                trades = self._run_batch_backtest(strategy, config)
            else:
                raise ValueError(f"不支持的回测模式: {config.mode}")

            # 计算每日收益
            daily_returns = self._calculate_daily_returns(trades, config)

            # 计算性能指标
            performance_metrics = self.calculate_performance_metrics(trades, daily_returns, config)

            # 创建回测结果
            result = BacktestResult(
                config=config,
                start_time=start_time,
                end_time=datetime.now(),
                total_trades=len(trades),
                winning_trades=len([t for t in trades if t['return'] > 0]),
                losing_trades=len([t for t in trades if t['return'] < 0]),
                total_return=performance_metrics.get('total_return', 0.0),
                annual_return=performance_metrics.get('annual_return', 0.0),
                max_drawdown=performance_metrics.get('max_drawdown', 0.0),
                sharpe_ratio=performance_metrics.get('sharpe_ratio', 0.0),
                win_rate=performance_metrics.get('win_rate', 0.0),
                avg_return_per_trade=performance_metrics.get('avg_return_per_trade', 0.0),
                avg_holding_days=performance_metrics.get('avg_holding_days', 0.0),
                benchmark_return=performance_metrics.get('benchmark_return', 0.0),
                excess_return=performance_metrics.get('excess_return', 0.0),
                trades=trades,
                daily_returns=daily_returns,
                performance_metrics=performance_metrics
            )

            self.logger.info(f"回测完成，总交易次数: {len(trades)}, 胜率: {result.win_rate:.1f}%")
            return result

        except Exception as e:
            self.logger.error(f"回测失败: {str(e)}")
            raise

    def _run_rolling_backtest(self, strategy: ISelectionStrategy, config: BacktestConfig) -> List[Dict]:
        """滚动回测"""
        trades = []
        current_date = config.start_date

        while current_date <= config.end_date:
            # 跳过周末
            if current_date.weekday() < 5:
                try:
                    # 执行策略选股
                    selections = self._run_strategy_on_date(strategy, current_date, config)

                    # 为每个选中的股票创建交易记录
                    for selection in selections:
                        trade = self._create_trade_record(selection, current_date, config)
                        if trade:
                            trades.append(trade)

                    if selections:
                        self.logger.info(f"{current_date.date()}: 选中 {len(selections)} 只股票")

                except Exception as e:
                    self.logger.warning(f"在日期 {current_date.date()} 执行策略失败: {str(e)}")

            current_date += timedelta(days=config.frequency)

        return trades

    def _run_single_backtest(self, strategy: ISelectionStrategy, config: BacktestConfig) -> List[Dict]:
        """单次回测"""
        # 在回测期间的中点执行一次策略
        mid_date = config.start_date + (config.end_date - config.start_date) / 2
        selections = self._run_strategy_on_date(strategy, mid_date, config)

        trades = []
        for selection in selections:
            trade = self._create_trade_record(selection, mid_date, config)
            if trade:
                trades.append(trade)

        return trades

    def _run_batch_backtest(self, strategy: ISelectionStrategy, config: BacktestConfig) -> List[Dict]:
        """批量回测"""
        # 在回测期间内每天都执行策略（工作日）
        trades = []
        current_date = config.start_date

        while current_date <= config.end_date:
            if current_date.weekday() < 5:
                try:
                    selections = self._run_strategy_on_date(strategy, current_date, config)
                    for selection in selections:
                        trade = self._create_trade_record(selection, current_date, config)
                        if trade:
                            trades.append(trade)
                except Exception as e:
                    self.logger.warning(f"在日期 {current_date.date()} 执行策略失败: {str(e)}")

            current_date += timedelta(days=1)

        return trades

    def _run_strategy_on_date(self, strategy: ISelectionStrategy, target_date: datetime,
                             config: BacktestConfig) -> List[Dict]:
        """在指定日期运行策略"""
        try:
            # 获取股票池
            if config.stock_pool:
                stock_codes = config.stock_pool
            else:
                stock_codes = self.data_access.get_all_stock_codes()
                # 限制股票数量以提高回测速度
                import random
                stock_codes = random.sample(stock_codes, min(200, len(stock_codes)))

            results = []

            # 计算需要的历史数据范围
            start_date = target_date - timedelta(days=60)  # 获取足够的历史数据

            for stock_code in stock_codes:
                try:
                    # 获取股票基本信息
                    stock_info = self.data_access.get_stock_info(stock_code)
                    if not stock_info:
                        continue

                    # 获取到目标日期为止的交易数据
                    trading_data = self.data_access.get_stock_data(stock_code, start_date, target_date)
                    if len(trading_data) < 30:  # 至少需要30天数据
                        continue

                    # 使用策略的内部分析方法
                    if hasattr(strategy, '_analyze_technical_reversal'):
                        result = strategy._analyze_technical_reversal(stock_code, stock_info, trading_data)
                        if result:
                            result['selection_date'] = target_date.date()
                            results.append(result)

                except Exception as e:
                    continue

            # 按评分排序并限制结果数量
            results.sort(key=lambda x: x.get('score', 0), reverse=True)
            return results[:config.max_positions]

        except Exception as e:
            self.logger.error(f"在日期 {target_date.date()} 运行策略失败: {str(e)}")
            return []

    def _create_trade_record(self, selection: Dict, entry_date: datetime,
                           config: BacktestConfig) -> Optional[Dict]:
        """创建交易记录"""
        try:
            stock_code = selection['stock_code']
            entry_price = float(selection['close_price'])

            # 计算退出日期
            exit_date = entry_date + timedelta(days=config.holding_days + 5)  # 多取几天确保有数据

            # 获取持有期间的数据
            future_data = self.data_access.get_stock_data(
                stock_code,
                entry_date + timedelta(days=1),
                exit_date
            )

            if not future_data:
                return None

            # 取前N个交易日作为持有期
            holding_data = future_data[:config.holding_days] if len(future_data) >= config.holding_days else future_data

            if not holding_data:
                return None

            # 计算收益
            exit_price = float(holding_data[-1]['close_price'])
            max_price = max(float(data['high_price']) for data in holding_data)
            min_price = min(float(data['low_price']) for data in holding_data)

            # 考虑手续费和滑点
            actual_entry_price = entry_price * (1 + config.slippage_rate + config.commission_rate)
            actual_exit_price = exit_price * (1 - config.slippage_rate - config.commission_rate)

            return_rate = (actual_exit_price / actual_entry_price - 1) * 100
            max_return = (max_price / actual_entry_price - 1) * 100
            max_drawdown = (min_price / actual_entry_price - 1) * 100

            return {
                'stock_code': stock_code,
                'stock_name': selection.get('stock_name', ''),
                'entry_date': entry_date.date(),
                'exit_date': holding_data[-1]['trade_date'],
                'entry_price': actual_entry_price,
                'exit_price': actual_exit_price,
                'max_price': max_price,
                'min_price': min_price,
                'return': return_rate,
                'max_return': max_return,
                'max_drawdown': max_drawdown,
                'holding_days': len(holding_data),
                'score': selection.get('score', 0),
                'reason': selection.get('reason', '')
            }

        except Exception as e:
            self.logger.warning(f"创建交易记录失败: {str(e)}")
            return None

    def _calculate_daily_returns(self, trades: List[Dict], config: BacktestConfig) -> List[Dict]:
        """计算每日收益"""
        if not trades:
            return []

        # 获取所有交易日期范围
        all_dates = set()
        for trade in trades:
            entry_date = trade['entry_date']
            exit_date = trade['exit_date']
            current_date = entry_date
            while current_date <= exit_date:
                if current_date.weekday() < 5:  # 工作日
                    all_dates.add(current_date)
                current_date += timedelta(days=1)

        # 按日期排序
        sorted_dates = sorted(all_dates)

        daily_returns = []
        portfolio_value = config.initial_capital

        for date in sorted_dates:
            # 计算当日持仓
            active_trades = [t for t in trades if t['entry_date'] <= date <= t['exit_date']]

            if active_trades:
                # 计算当日收益
                daily_return = 0
                for trade in active_trades:
                    # 简化计算：假设等权重分配
                    weight = 1.0 / len(active_trades)
                    if date == trade['exit_date']:
                        daily_return += weight * trade['return'] / 100

                portfolio_value *= (1 + daily_return)

                daily_returns.append({
                    'date': date,
                    'return': daily_return * 100,
                    'portfolio_value': portfolio_value,
                    'active_positions': len(active_trades)
                })
            else:
                daily_returns.append({
                    'date': date,
                    'return': 0.0,
                    'portfolio_value': portfolio_value,
                    'active_positions': 0
                })

        return daily_returns

    def calculate_performance_metrics(self, trades: List[Dict],
                                    daily_returns: List[Dict],
                                    config: BacktestConfig) -> Dict:
        """计算性能指标"""
        if not trades:
            return {
                'total_return': 0.0,
                'annual_return': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'win_rate': 0.0,
                'avg_return_per_trade': 0.0,
                'avg_holding_days': 0.0,
                'benchmark_return': 0.0,
                'excess_return': 0.0
            }

        # 基本统计
        returns = [trade['return'] for trade in trades]
        winning_trades = [r for r in returns if r > 0]

        total_return = sum(returns)
        win_rate = len(winning_trades) / len(returns) * 100 if returns else 0
        avg_return_per_trade = np.mean(returns) if returns else 0
        avg_holding_days = np.mean([trade['holding_days'] for trade in trades]) if trades else 0

        # 年化收益率
        days_diff = (config.end_date - config.start_date).days
        years = days_diff / 365.25 if days_diff > 0 else 1
        annual_return = (total_return / len(trades)) * (252 / avg_holding_days) if avg_holding_days > 0 else 0

        # 最大回撤
        max_drawdown = 0
        if daily_returns:
            peak_value = config.initial_capital
            for daily_return in daily_returns:
                current_value = daily_return['portfolio_value']
                if current_value > peak_value:
                    peak_value = current_value
                drawdown = (peak_value - current_value) / peak_value * 100
                max_drawdown = max(max_drawdown, drawdown)

        # 夏普比率
        sharpe_ratio = 0
        if returns and len(returns) > 1:
            std_return = np.std(returns)
            if std_return > 0:
                sharpe_ratio = avg_return_per_trade / std_return

        # 基准收益（简化处理）
        benchmark_return = 0  # 可以后续扩展
        excess_return = annual_return - benchmark_return

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'win_rate': win_rate,
            'avg_return_per_trade': avg_return_per_trade,
            'avg_holding_days': avg_holding_days,
            'benchmark_return': benchmark_return,
            'excess_return': excess_return,
            'total_trades': len(trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(trades) - len(winning_trades),
            'best_trade': max(returns) if returns else 0,
            'worst_trade': min(returns) if returns else 0,
            'profit_factor': sum(winning_trades) / abs(sum([r for r in returns if r < 0])) if any(r < 0 for r in returns) else float('inf')
        }

    def save_backtest_result(self, result: BacktestResult) -> bool:
        """保存回测结果"""
        # TODO: 实现回测结果保存到数据库
        self.logger.info("回测结果保存功能待实现")
        return True

    def get_backtest_results(self, strategy_name: str = None,
                           start_date: datetime = None,
                           end_date: datetime = None) -> List[BacktestResult]:
        """获取历史回测结果"""
        # TODO: 实现从数据库获取历史回测结果
        self.logger.info("获取历史回测结果功能待实现")
        return []