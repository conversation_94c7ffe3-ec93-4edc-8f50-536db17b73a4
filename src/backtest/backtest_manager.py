"""
回测管理器
"""
import logging
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from ..core.interfaces.backtest import BacktestConfig, BacktestResult, BacktestMode
from ..core.interfaces.data_access import IDataAccess
from .backtest_engine import BacktestEngine


class BacktestManager:
    """回测管理器"""
    
    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.backtest_engine = BacktestEngine(data_access)
        self.logger = logging.getLogger(__name__)
    
    def run_strategy_backtest(self, strategy_name: str, 
                            start_date: datetime, 
                            end_date: datetime,
                            **kwargs) -> BacktestResult:
        """运行策略回测"""
        
        # 创建回测配置
        config = BacktestConfig(
            strategy_name=strategy_name,
            start_date=start_date,
            end_date=end_date,
            mode=kwargs.get('mode', BacktestMode.ROLLING),
            frequency=kwargs.get('frequency', 7),
            holding_days=kwargs.get('holding_days', 10),
            initial_capital=kwargs.get('initial_capital', 100000.0),
            max_positions=kwargs.get('max_positions', 10),
            commission_rate=kwargs.get('commission_rate', 0.0003),
            slippage_rate=kwargs.get('slippage_rate', 0.001),
            benchmark=kwargs.get('benchmark', "000001.SH"),
            stock_pool=kwargs.get('stock_pool', None)
        )
        
        self.logger.info(f"开始回测策略: {strategy_name}")
        self.logger.info(f"回测配置: {config}")
        
        # 执行回测
        result = self.backtest_engine.run_backtest(config)
        
        # 保存回测结果
        self.backtest_engine.save_backtest_result(result)
        
        return result
    
    def run_batch_backtest(self, strategies: List[str],
                          start_date: datetime,
                          end_date: datetime,
                          **kwargs) -> Dict[str, BacktestResult]:
        """批量回测多个策略"""
        results = {}
        
        for strategy_name in strategies:
            try:
                self.logger.info(f"开始回测策略: {strategy_name}")
                result = self.run_strategy_backtest(
                    strategy_name, start_date, end_date, **kwargs
                )
                results[strategy_name] = result
                self.logger.info(f"策略 {strategy_name} 回测完成")
                
            except Exception as e:
                self.logger.error(f"策略 {strategy_name} 回测失败: {str(e)}")
                continue
        
        return results
    
    def run_rolling_window_backtest(self, strategy_name: str,
                                   start_date: datetime,
                                   end_date: datetime,
                                   window_months: int = 6,
                                   **kwargs) -> List[BacktestResult]:
        """滚动窗口回测"""
        results = []
        current_start = start_date
        
        while current_start < end_date:
            # 计算当前窗口的结束日期
            current_end = current_start + timedelta(days=window_months * 30)
            if current_end > end_date:
                current_end = end_date
            
            try:
                self.logger.info(f"滚动窗口回测: {current_start.date()} 到 {current_end.date()}")
                result = self.run_strategy_backtest(
                    strategy_name, current_start, current_end, **kwargs
                )
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"滚动窗口回测失败: {str(e)}")
            
            # 移动到下一个窗口
            current_start = current_start + timedelta(days=30)  # 每月滚动一次
        
        return results
    
    def generate_backtest_report(self, result: BacktestResult) -> str:
        """生成回测报告"""
        report = []
        report.append("=" * 80)
        report.append(f"策略回测报告: {result.config.strategy_name}")
        report.append("=" * 80)
        
        # 基本信息
        report.append(f"\n基本信息:")
        report.append(f"策略名称: {result.config.strategy_name}")
        report.append(f"回测期间: {result.config.start_date.date()} 到 {result.config.end_date.date()}")
        report.append(f"回测模式: {result.config.mode.value}")
        report.append(f"持有天数: {result.config.holding_days}")
        report.append(f"初始资金: {result.config.initial_capital:,.2f}")
        
        # 交易统计
        report.append(f"\n交易统计:")
        report.append(f"总交易次数: {result.total_trades}")
        report.append(f"盈利交易: {result.winning_trades}")
        report.append(f"亏损交易: {result.losing_trades}")
        report.append(f"胜率: {result.win_rate:.1f}%")
        report.append(f"平均持有天数: {result.avg_holding_days:.1f}")
        
        # 收益指标
        report.append(f"\n收益指标:")
        report.append(f"总收益率: {result.total_return:.2f}%")
        report.append(f"年化收益率: {result.annual_return:.2f}%")
        report.append(f"平均每笔收益: {result.avg_return_per_trade:.2f}%")
        report.append(f"最大回撤: {result.max_drawdown:.2f}%")
        report.append(f"夏普比率: {result.sharpe_ratio:.2f}")
        
        # 风险指标
        if result.performance_metrics:
            metrics = result.performance_metrics
            report.append(f"\n风险指标:")
            report.append(f"最佳交易: {metrics.get('best_trade', 0):.2f}%")
            report.append(f"最差交易: {metrics.get('worst_trade', 0):.2f}%")
            report.append(f"盈亏比: {metrics.get('profit_factor', 0):.2f}")
        
        # 详细交易记录（显示前10笔）
        if result.trades:
            report.append(f"\n详细交易记录 (前10笔):")
            report.append("-" * 80)
            report.append(f"{'股票代码':<10} {'买入日期':<12} {'卖出日期':<12} {'收益率':<10} {'评分':<8}")
            report.append("-" * 80)
            
            for i, trade in enumerate(result.trades[:10]):
                report.append(
                    f"{trade['stock_code']:<10} "
                    f"{trade['entry_date']:<12} "
                    f"{trade['exit_date']:<12} "
                    f"{trade['return']:>8.2f}% "
                    f"{trade['score']:>6.1f}"
                )
        
        # 策略评估
        report.append(f"\n策略评估:")
        report.append("-" * 50)
        
        if result.win_rate > 60:
            report.append("✓ 策略表现优秀，胜率超过60%")
        elif result.win_rate > 50:
            report.append("✓ 策略表现良好，胜率超过50%")
        elif result.win_rate > 40:
            report.append("△ 策略表现一般，胜率在40-50%之间")
        else:
            report.append("✗ 策略表现较差，胜率低于40%")
        
        if result.avg_return_per_trade > 3:
            report.append("✓ 平均收益表现优秀，超过3%")
        elif result.avg_return_per_trade > 1:
            report.append("✓ 平均收益表现良好，超过1%")
        elif result.avg_return_per_trade > 0:
            report.append("△ 平均收益为正，但较低")
        else:
            report.append("✗ 平均收益为负")
        
        if result.max_drawdown < 5:
            report.append("✓ 风险控制良好，最大回撤小于5%")
        elif result.max_drawdown < 10:
            report.append("△ 风险控制一般，最大回撤在5-10%之间")
        else:
            report.append("✗ 风险较高，最大回撤超过10%")
        
        return "\n".join(report)
    
    def compare_strategies(self, results: Dict[str, BacktestResult]) -> str:
        """比较多个策略的回测结果"""
        if not results:
            return "没有回测结果可比较"
        
        report = []
        report.append("=" * 100)
        report.append("策略比较报告")
        report.append("=" * 100)
        
        # 表头
        report.append(f"\n{'策略名称':<20} {'总交易':<8} {'胜率':<8} {'平均收益':<10} {'年化收益':<10} {'最大回撤':<10} {'夏普比率':<10}")
        report.append("-" * 100)
        
        # 策略数据
        for strategy_name, result in results.items():
            report.append(
                f"{strategy_name:<20} "
                f"{result.total_trades:<8} "
                f"{result.win_rate:<7.1f}% "
                f"{result.avg_return_per_trade:<9.2f}% "
                f"{result.annual_return:<9.2f}% "
                f"{result.max_drawdown:<9.2f}% "
                f"{result.sharpe_ratio:<10.2f}"
            )
        
        # 排名
        report.append(f"\n策略排名:")
        report.append("-" * 50)
        
        # 按夏普比率排名
        sorted_by_sharpe = sorted(results.items(), key=lambda x: x[1].sharpe_ratio, reverse=True)
        report.append(f"按夏普比率排名:")
        for i, (name, result) in enumerate(sorted_by_sharpe, 1):
            report.append(f"  {i}. {name}: {result.sharpe_ratio:.2f}")
        
        # 按胜率排名
        sorted_by_winrate = sorted(results.items(), key=lambda x: x[1].win_rate, reverse=True)
        report.append(f"\n按胜率排名:")
        for i, (name, result) in enumerate(sorted_by_winrate, 1):
            report.append(f"  {i}. {name}: {result.win_rate:.1f}%")
        
        return "\n".join(report)
