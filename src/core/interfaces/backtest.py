"""
回测相关接口定义
"""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum


class BacktestMode(Enum):
    """回测模式"""
    SINGLE = "single"  # 单次回测
    ROLLING = "rolling"  # 滚动回测
    BATCH = "batch"  # 批量回测


@dataclass
class BacktestConfig:
    """回测配置"""
    strategy_name: str  # 策略名称
    start_date: datetime  # 开始日期
    end_date: datetime  # 结束日期
    mode: BacktestMode = BacktestMode.ROLLING  # 回测模式
    frequency: int = 7  # 回测频率（天）
    holding_days: int = 10  # 持有天数
    initial_capital: float = 100000.0  # 初始资金
    max_positions: int = 10  # 最大持仓数
    commission_rate: float = 0.0003  # 手续费率
    slippage_rate: float = 0.001  # 滑点率
    benchmark: str = "000001.SH"  # 基准指数
    stock_pool: Optional[List[str]] = None  # 股票池，None表示全市场
    
    def __post_init__(self):
        """验证配置参数"""
        if self.start_date >= self.end_date:
            raise ValueError("开始日期必须早于结束日期")
        if self.frequency <= 0:
            raise ValueError("回测频率必须大于0")
        if self.holding_days <= 0:
            raise ValueError("持有天数必须大于0")
        if self.initial_capital <= 0:
            raise ValueError("初始资金必须大于0")


@dataclass
class BacktestResult:
    """回测结果"""
    config: BacktestConfig  # 回测配置
    start_time: datetime  # 回测开始时间
    end_time: datetime  # 回测结束时间
    total_trades: int  # 总交易次数
    winning_trades: int  # 盈利交易次数
    losing_trades: int  # 亏损交易次数
    total_return: float  # 总收益率
    annual_return: float  # 年化收益率
    max_drawdown: float  # 最大回撤
    sharpe_ratio: float  # 夏普比率
    win_rate: float  # 胜率
    avg_return_per_trade: float  # 平均每笔收益率
    avg_holding_days: float  # 平均持有天数
    benchmark_return: float  # 基准收益率
    excess_return: float  # 超额收益率
    trades: List[Dict]  # 详细交易记录
    daily_returns: List[Dict]  # 每日收益记录
    performance_metrics: Dict  # 其他性能指标


class IBacktestEngine(ABC):
    """回测引擎抽象接口"""
    
    @abstractmethod
    def run_backtest(self, config: BacktestConfig) -> BacktestResult:
        """
        运行回测
        
        Args:
            config: 回测配置
            
        Returns:
            BacktestResult: 回测结果
        """
        pass
    
    @abstractmethod
    def calculate_performance_metrics(self, trades: List[Dict], 
                                    daily_returns: List[Dict],
                                    config: BacktestConfig) -> Dict:
        """
        计算性能指标
        
        Args:
            trades: 交易记录
            daily_returns: 每日收益记录
            config: 回测配置
            
        Returns:
            Dict: 性能指标
        """
        pass
    
    @abstractmethod
    def save_backtest_result(self, result: BacktestResult) -> bool:
        """
        保存回测结果
        
        Args:
            result: 回测结果
            
        Returns:
            bool: 保存是否成功
        """
        pass
    
    @abstractmethod
    def get_backtest_results(self, strategy_name: str = None,
                           start_date: datetime = None,
                           end_date: datetime = None) -> List[BacktestResult]:
        """
        获取历史回测结果
        
        Args:
            strategy_name: 策略名称，None表示所有策略
            start_date: 开始日期，None表示不限制
            end_date: 结束日期，None表示不限制
            
        Returns:
            List[BacktestResult]: 回测结果列表
        """
        pass
