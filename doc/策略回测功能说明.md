# 策略验证功能说明

## 功能概述

策略验证功能是A股选股系统的重要组成部分，用于测试选股策略在历史数据中是否能正常工作，验证策略逻辑的正确性和实用性。

**注意：这不是传统的投资回测功能，而是策略调试和验证工具。**

## 核心特性

### 1. 多种验证模式
- **单日验证**: 测试策略在特定日期的选股表现
- **批量验证**: 测试策略在一段时间内的选股稳定性
- **指定日期验证**: 测试策略在关键时间点的表现
- **策略比较**: 比较多个策略的验证结果
- **稳定性验证**: 分析策略在不同时间窗口的稳定性

### 2. 详细的选股信息
- 选中股票列表及其技术指标
- RSI、量比等关键指标值
- 策略评分和选股原因
- 选股数量和比例统计

### 3. 验证结果记录
- 保存验证结果到数据库
- 支持历史验证记录查询
- 生成详细的验证报告

### 4. 策略调试支持
- 识别策略参数是否合理
- 检测策略条件是否过于严格
- 验证策略逻辑是否正确

## 使用方法

### 1. 命令行工具

#### 基本用法
```bash
# 单日验证
python scripts/run_validation.py technical_reversal single \
    --test-date 2024-04-07

# 批量验证
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 7

# 指定日期验证
python scripts/run_validation.py technical_reversal dates \
    --test-dates "2024-04-07,2024-04-30,2024-05-15"

# 策略比较
python scripts/run_validation.py technical_reversal compare \
    --strategies "technical_reversal,volume_anomaly" \
    --start-date 2024-01-01 \
    --end-date 2024-06-30

# 稳定性验证
python scripts/run_validation.py technical_reversal stability \
    --start-date 2024-01-01 \
    --end-date 2024-12-31 \
    --window-days 30
```

#### 高级参数
```bash
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 7 \
    --max-stocks 15 \
    --stock-pool "000001,000002,600000" \
    --output validation_report.txt \
    --verbose \
    --no-save
```

### 2. Python API

#### 单策略回测
```python
from datetime import datetime
from src.backtest.backtest_manager import BacktestManager
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

# 初始化
config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)
backtest_manager = BacktestManager(data_access)

# 执行回测
result = backtest_manager.run_strategy_backtest(
    strategy_name="technical_reversal",
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30),
    holding_days=10,
    max_positions=5
)

# 生成报告
report = backtest_manager.generate_backtest_report(result)
print(report)
```

#### 批量回测
```python
# 批量回测多个策略
results = backtest_manager.run_batch_backtest(
    strategies=["technical_reversal", "volume_anomaly"],
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30)
)

# 策略比较
comparison = backtest_manager.compare_strategies(results)
print(comparison)
```

#### 滚动窗口回测
```python
# 滚动窗口回测
results = backtest_manager.run_rolling_window_backtest(
    strategy_name="technical_reversal",
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31),
    window_months=3
)
```

## 配置参数说明

### BacktestConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| strategy_name | str | - | 策略名称 |
| start_date | datetime | - | 回测开始日期 |
| end_date | datetime | - | 回测结束日期 |
| mode | BacktestMode | ROLLING | 回测模式 |
| frequency | int | 7 | 回测频率（天） |
| holding_days | int | 10 | 持有天数 |
| initial_capital | float | 100000.0 | 初始资金 |
| max_positions | int | 10 | 最大持仓数 |
| commission_rate | float | 0.0003 | 手续费率 |
| slippage_rate | float | 0.001 | 滑点率 |
| benchmark | str | "000001.SH" | 基准指数 |
| stock_pool | List[str] | None | 股票池 |

## 性能指标说明

### 收益指标
- **总收益率**: 整个回测期间的累计收益率
- **年化收益率**: 按年计算的收益率
- **平均每笔收益**: 所有交易的平均收益率

### 风险指标
- **最大回撤**: 从峰值到谷值的最大跌幅
- **夏普比率**: 风险调整后的收益率
- **胜率**: 盈利交易占总交易的比例

### 交易指标
- **总交易次数**: 回测期间的总交易数
- **平均持有天数**: 平均每笔交易的持有时间
- **盈亏比**: 平均盈利与平均亏损的比值

## 回测报告示例

```
================================================================================
策略回测报告: technical_reversal
================================================================================

基本信息:
策略名称: technical_reversal
回测期间: 2024-01-01 到 2024-06-30
回测模式: rolling
持有天数: 10
初始资金: 100,000.00

交易统计:
总交易次数: 25
盈利交易: 18
亏损交易: 7
胜率: 72.0%
平均持有天数: 9.2

收益指标:
总收益率: 15.60%
年化收益率: 31.20%
平均每笔收益: 0.62%
最大回撤: 3.45%
夏普比率: 1.85

风险指标:
最佳交易: 8.90%
最差交易: -2.10%
盈亏比: 3.25

策略评估:
✓ 策略表现优秀，胜率超过60%
✓ 平均收益表现良好，超过1%
✓ 风险控制良好，最大回撤小于5%
```

## 注意事项

### 1. 数据质量
- 确保历史数据完整性
- 注意停牌股票的处理
- 考虑除权除息的影响

### 2. 回测偏差
- **前瞻偏差**: 避免使用未来信息
- **生存偏差**: 考虑退市股票
- **流动性偏差**: 考虑实际交易的流动性限制

### 3. 参数设置
- 合理设置手续费和滑点
- 考虑实际的资金容量限制
- 股票池的选择要符合实际情况

### 4. 结果解读
- 回测结果不代表未来表现
- 需要结合市场环境分析
- 关注策略的稳定性和适应性

## 扩展功能

### 1. 自定义指标
可以扩展 `calculate_performance_metrics` 方法添加自定义指标：
- 信息比率
- 卡尔马比率
- 索提诺比率

### 2. 基准比较
支持与市场指数进行比较：
- 相对收益
- 跟踪误差
- 信息比率

### 3. 风险分析
- VaR (Value at Risk)
- 最大连续亏损
- 波动率分析

## 最佳实践

1. **多时间段验证**: 在不同市场环境下测试策略
2. **参数敏感性分析**: 测试关键参数的影响
3. **样本外测试**: 保留部分数据用于最终验证
4. **定期更新**: 随着新数据的积累定期重新回测
5. **组合测试**: 测试多策略组合的效果

## 故障排除

### 常见问题
1. **数据不足**: 确保回测期间有足够的历史数据
2. **策略无选股**: 检查策略参数是否过于严格
3. **性能问题**: 限制股票池大小或调整回测频率
4. **内存不足**: 分批处理或减少回测时间跨度

### 日志查看
回测过程中的详细日志保存在 `logs/` 目录下，可以查看具体的执行情况和错误信息。
